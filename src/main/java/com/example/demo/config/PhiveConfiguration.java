package com.example.demo.config;

import com.helger.commons.io.resource.ClassPathResource;
import com.helger.diver.api.coord.DVRCoordinate;
import com.helger.phive.api.executorset.ValidationExecutorSet;
import com.helger.phive.api.executorset.ValidationExecutorSetRegistry;
import com.helger.phive.api.executorset.status.ValidationExecutorSetStatus;
import com.helger.phive.xml.schematron.ValidationExecutorSchematron;
import com.helger.phive.xml.source.IValidationSourceXML;
import com.helger.phive.xml.xsd.ValidationExecutorXSD;
import com.helger.xml.namespace.MapBasedNamespaceContext;
import com.helger.xml.namespace.IIterableNamespaceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;


/**
 * Complete PHIVE Configuration for Spring Boot
 *
 * This configuration class sets up a comprehensive PHIVE validation registry similar to Peppol e-invoice validation.
 * It includes XSD schema validation and Schematron business rules validation with multiple validation layers.
 *
 * Features:
 * - Multi-layer validation (XSD + multiple Schematron rule sets)
 * - Business rule validation similar to Peppol BIS 3.0
 * - Field-level validation with detailed error reporting
 * - Support for different document types (Invoice, Credit Note, etc.)
 */
@Configuration
public class PhiveConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(PhiveConfiguration.class);

    /**
     * Creates the main validation executor set registry with comprehensive validation rules
     */
    @Bean
    @Primary
    public ValidationExecutorSetRegistry<IValidationSourceXML> validationRegistry() {
        ValidationExecutorSetRegistry<IValidationSourceXML> registry = new ValidationExecutorSetRegistry<>();

        // Register all validation rule sets
        registerValidationRules(registry);

        return registry;
    }

    /**
     * Register comprehensive validation rules similar to Peppol e-invoice validation
     */
    private void registerValidationRules(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        LOGGER.info("Registering comprehensive PHIVE validation rules (Peppol-style)...");

        try {
            // 1. Basic XSD validation
            registerBasicXSDValidation(registry);

            // 2. Simple example validation
            registerSimpleExampleValidation(registry);

            // 3. E-Invoice validation with business rules (Peppol-style)
            registerEInvoiceValidation(registry);

            // 4. Credit Note validation with business rules
            registerCreditNoteValidation(registry);

            // 5. Purchase Order validation
            registerPurchaseOrderValidation(registry);

            // 6. Custom document validation with advanced rules
            registerCustomDocValidation(registry);

            LOGGER.info("Successfully registered {} validation executor sets with comprehensive business rules",
                       registry.getAll().size());
        } catch (Exception e) {
            LOGGER.error("Failed to register validation rules", e);
        }
    }

    /**
     * Register basic XSD-only validation - CORRECTED VERSION
     */
    private void registerBasicXSDValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                    new ClassPathResource("schemas/basic-example.xsd"));

            // Use the constructor instead of create() method (which doesn't exist in PHIVE 9.2.1)
            ValidationExecutorSet<IValidationSourceXML> basicVES = new ValidationExecutorSet<>(
                    DVRCoordinate.create("com.example", "basic-xsd", "1.0"),
                    "Basic XSD Validation",
                    ValidationExecutorSetStatus.createValidNow(),
                    xsdValidator);

            registry.registerValidationExecutorSet(basicVES);
            LOGGER.info("Registered basic XSD validation: com.example:basic-xsd:1.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register basic XSD validation: {}", e.getMessage());
        }
    }

    /**
     * Register simple example validation
     */
    private void registerSimpleExampleValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/simple-example.xsd"));

            ValidationExecutorSet<IValidationSourceXML> simpleVES = ValidationExecutorSet.create(
                DVRCoordinate.create("com.example", "simple", "1.0"),
                "Simple Example Validation",
                ValidationExecutorSetStatus.createValidNow(),
                xsdValidator);

            registry.registerValidationExecutorSet(simpleVES);
            LOGGER.info("Registered simple example validation: com.example:simple:1.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register simple example validation: {}", e.getMessage());
        }
    }

    /**
     * Register E-Invoice validation with comprehensive business rules (Peppol-style)
     * This includes XSD validation + multiple Schematron rule layers
     */
    private void registerEInvoiceValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            // 1. XSD Schema validation
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                    new ClassPathResource("schemas/einvoice/UBL-Invoice-2.1.xsd"));

            // 2. Schematron business rules - using correct method signature
            ValidationExecutorSchematron basicRulesValidator = ValidationExecutorSchematron.createXSLT(
                    new ClassPathResource("schematron/einvoice/basic-business-rules.xslt"),
                    (IIterableNamespaceContext) null);

            ValidationExecutorSchematron advancedRulesValidator = ValidationExecutorSchematron.createXSLT(
                    new ClassPathResource("schematron/einvoice/advanced-validation-rules.xslt"),
                    (IIterableNamespaceContext) null);

            ValidationExecutorSchematron peppolRulesValidator = ValidationExecutorSchematron.createXSLT(
                    new ClassPathResource("schematron/einvoice/peppol-bis-3.0-rules.xslt"),
                    (IIterableNamespaceContext) null);

            // Create multi-layer validation executor set using create() method
            ValidationExecutorSet<IValidationSourceXML> eInvoiceVES = ValidationExecutorSet.create(
                    DVRCoordinate.create("org.peppol.bis", "einvoice", "3.0"),
                    "E-Invoice Validation (Peppol BIS 3.0 Style)",
                    ValidationExecutorSetStatus.createValidNow(),
                    xsdValidator,
                    basicRulesValidator,
                    advancedRulesValidator,
                    peppolRulesValidator);

            registry.registerValidationExecutorSet(eInvoiceVES);
            LOGGER.info("Registered E-Invoice validation with 4 layers: org.peppol.bis:einvoice:3.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register E-Invoice validation (some rule files may be missing): {}", e.getMessage());
            // Register with XSD only as fallback
            registerFallbackEInvoiceValidation(registry);
        }
    }

    /**
     * Fallback E-Invoice validation with XSD only
     */
    private void registerFallbackEInvoiceValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/einvoice/UBL-Invoice-2.1-fallback.xsd"));

            ValidationExecutorSet<IValidationSourceXML> fallbackVES = ValidationExecutorSet.create(
                DVRCoordinate.create("org.peppol.bis", "einvoice-fallback", "3.0"),
                "E-Invoice Validation (XSD Only - Fallback)",
                ValidationExecutorSetStatus.createValidNow(),
                xsdValidator);

            registry.registerValidationExecutorSet(fallbackVES);
            LOGGER.info("Registered fallback E-Invoice validation: org.peppol.bis:einvoice-fallback:3.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register fallback E-Invoice validation: {}", e.getMessage());
        }
    }

    /**
     * Register Credit Note validation with business rules
     */
    private void registerCreditNoteValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            // XSD validation
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/creditnote/UBL-CreditNote-2.1.xsd"));

            // Business rules validation
            ValidationExecutorSchematron businessRulesValidator = ValidationExecutorSchematron.createXSLT(
                new ClassPathResource("schematron/creditnote/credit-note-business-rules.xslt"),
                (IIterableNamespaceContext) null);

            ValidationExecutorSet<IValidationSourceXML> creditNoteVES = ValidationExecutorSet.create(
                DVRCoordinate.create("org.peppol.bis", "creditnote", "3.0"),
                "Credit Note Validation (Peppol BIS 3.0)",
                ValidationExecutorSetStatus.createValidNow(),
                xsdValidator,
                businessRulesValidator);

            registry.registerValidationExecutorSet(creditNoteVES);
            LOGGER.info("Registered Credit Note validation: org.peppol.bis:creditnote:3.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register Credit Note validation: {}", e.getMessage());
        }
    }

    /**
     * Register Purchase Order validation
     */
    private void registerPurchaseOrderValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/order/UBL-Order-2.1.xsd"));

            ValidationExecutorSchematron businessRulesValidator = ValidationExecutorSchematron.createXSLT(
                new ClassPathResource("schematron/order/purchase-order-rules.xslt"),
                (IIterableNamespaceContext) null);

            ValidationExecutorSet<IValidationSourceXML> orderVES = ValidationExecutorSet.create(
                DVRCoordinate.create("org.peppol.bis", "order", "3.0"),
                "Purchase Order Validation",
                ValidationExecutorSetStatus.createValidNow(),
                xsdValidator,
                businessRulesValidator);

            registry.registerValidationExecutorSet(orderVES);
            LOGGER.info("Registered Purchase Order validation: org.peppol.bis:order:3.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register Purchase Order validation: {}", e.getMessage());
        }
    }

    /**
     * Register custom document validation (XSD + Schematron)
     */
    private void registerCustomDocValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            // Custom document XSD validation
            ValidationExecutorXSD customXsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/custom/custom-document.xsd"));

            // Create validation executor set with just XSD validation first
            ValidationExecutorSet<IValidationSourceXML> customVES = ValidationExecutorSet.create(
                DVRCoordinate.create("com.example", "custom-doc", "1.0"),
                "Custom Document Validation",
                ValidationExecutorSetStatus.createValidNow(),
                customXsdValidator);

            // Try to add Schematron validation if available
            try {
                // Create namespace context for Schematron
                MapBasedNamespaceContext aNamespaceContext = new MapBasedNamespaceContext();
                aNamespaceContext.addMapping("c", "urn:example:custom");

                // Custom document Schematron rules
                ValidationExecutorSchematron customSchematronValidator = ValidationExecutorSchematron.createXSLT(
                    new ClassPathResource("schematron/custom/custom-rules.xslt"),
                    aNamespaceContext);

                // Create a new VES with both validators
                customVES = ValidationExecutorSet.create(
                    DVRCoordinate.create("com.example", "custom-doc", "1.0"),
                    "Custom Document Validation (XSD + Schematron)",
                    ValidationExecutorSetStatus.createValidNow(),
                    customXsdValidator,
                    customSchematronValidator);

                LOGGER.info("Added Schematron validation to Custom Document validation");
            } catch (Exception e) {
                LOGGER.warn("Could not add Schematron validation to Custom Document (XSLT file may be missing): {}", e.getMessage());
            }

            registry.registerValidationExecutorSet(customVES);
            LOGGER.info("Registered Custom Document validation: com.example:custom-doc:1.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register Custom Document validation (schema files may be missing): {}", e.getMessage());
        }
    }
}