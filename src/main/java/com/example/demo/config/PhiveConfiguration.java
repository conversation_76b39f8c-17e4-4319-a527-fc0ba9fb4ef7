package com.example.demo.config;

import com.helger.commons.io.resource.ClassPathResource;
import com.helger.diver.api.coord.DVRCoordinate;
import com.helger.phive.api.executorset.ValidationExecutorSet;
import com.helger.phive.api.executorset.ValidationExecutorSetRegistry;
import com.helger.phive.api.executorset.status.ValidationExecutorSetStatus;
import com.helger.phive.xml.schematron.ValidationExecutorSchematron;
import com.helger.phive.xml.source.IValidationSourceXML;
import com.helger.phive.xml.xsd.ValidationExecutorXSD;
import com.helger.xml.namespace.MapBasedNamespaceContext;
import com.helger.xml.namespace.IIterableNamespaceContext;

import com.helger.phive.peppol.PeppolValidation3_15_0;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Primary;


/**
 * Complete PHIVE Configuration for Spring Boot
 *
 * This configuration class sets up a comprehensive PHIVE validation registry similar to Peppol e-invoice validation.
 * It includes XSD schema validation and Schematron business rules validation with multiple validation layers.
 *
 * Features:
 * - Multi-layer validation (XSD + multiple Schematron rule sets)
 * - Business rule validation similar to Peppol BIS 3.0
 * - Field-level validation with detailed error reporting
 * - Support for different document types (Invoice, Credit Note, etc.)
 */
@Configuration
public class PhiveConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(PhiveConfiguration.class);

    /**
     * Creates the main validation executor set registry with comprehensive validation rules
     */
    @Bean
    @Primary
    public ValidationExecutorSetRegistry<IValidationSourceXML> validationRegistry() {
        ValidationExecutorSetRegistry<IValidationSourceXML> registry = new ValidationExecutorSetRegistry<>();

        // Register all validation rule sets
        registerValidationRules(registry);

        return registry;
    }

    /**
     * Register comprehensive validation rules similar to Peppol e-invoice validation
     */
    private void registerValidationRules(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        LOGGER.info("Registering comprehensive PHIVE validation rules (Peppol-style)...");

        try {
            // 1. Basic XSD validation
            registerBasicXSDValidation(registry);

            // 2. Peppol Invoice validation (built-in rules)
            registerPeppolInvoiceValidation(registry);

            // 3. Peppol Credit Note validation (built-in rules)
            registerPeppolCreditNoteValidation(registry);

            // 4. Custom document validation with advanced rules
            registerCustomDocValidation(registry);

            LOGGER.info("Successfully registered {} validation executor sets with comprehensive business rules",
                       registry.getAll().size());
        } catch (Exception e) {
            LOGGER.error("Failed to register validation rules", e);
        }
    }

    /**
     * Register basic XSD-only validation - PHIVE 9.2.2 VERSION
     */
    private void registerBasicXSDValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                    new ClassPathResource("schemas/basic-example.xsd"));

            // Use PHIVE 9.2.2 constructor pattern (builder may not be available)
            ValidationExecutorSet<IValidationSourceXML> basicVES = new ValidationExecutorSet<>(
                    DVRCoordinate.create("com.example", "basic-xsd", "1.0"),
                    "Basic XSD Validation",
                    ValidationExecutorSetStatus.createValidNow());
            basicVES.addExecutor(xsdValidator);

            registry.registerValidationExecutorSet(basicVES);
            LOGGER.info("Registered basic XSD validation: com.example:basic-xsd:1.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register basic XSD validation: {}", e.getMessage());
        }
    }

    /**
     * Register Peppol BIS 3.0 Invoice validation using built-in rules
     */
    private void registerPeppolInvoiceValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            // Use the built-in Peppol validation rules from phive-rules-peppol 3.1.13
            registry.registerValidationExecutorSet(PeppolValidation3_15_0.VID_OPENPEPPOL_INVOICE_UBL_V3);
            LOGGER.info("Registered Peppol Invoice validation: {}", PeppolValidation3_15_0.VID_OPENPEPPOL_INVOICE_UBL_V3.getID());

        } catch (Exception e) {
            LOGGER.warn("Could not register Peppol Invoice validation: {}", e.getMessage());
            // Fallback to basic invoice validation
            registerFallbackInvoiceValidation(registry);
        }
    }

    /**
     * Register Peppol BIS 3.0 Credit Note validation using built-in rules
     */
    private void registerPeppolCreditNoteValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            // Use the built-in Peppol validation rules from phive-rules-peppol 3.1.13
            registry.registerValidationExecutorSet(PeppolValidation3_15_0.VID_OPENPEPPOL_CREDIT_NOTE_UBL_V3);
            LOGGER.info("Registered Peppol Credit Note validation: {}", PeppolValidation3_15_0.VID_OPENPEPPOL_CREDIT_NOTE_UBL_V3.getID());

        } catch (Exception e) {
            LOGGER.warn("Could not register Peppol Credit Note validation: {}", e.getMessage());
            // Fallback to basic credit note validation
            registerFallbackCreditNoteValidation(registry);
        }
    }

    /**
     * Fallback Invoice validation with basic XSD only
     */
    private void registerFallbackInvoiceValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/invoice/UBL-Invoice-2.1.xsd"));

            ValidationExecutorSet<IValidationSourceXML> fallbackVES = new ValidationExecutorSet<>(
                DVRCoordinate.create("org.peppol.bis", "invoice-fallback", "3.0"),
                "Peppol Invoice Validation (XSD Only - Fallback)",
                ValidationExecutorSetStatus.createValidNow());
            fallbackVES.addExecutor(xsdValidator);

            registry.registerValidationExecutorSet(fallbackVES);
            LOGGER.info("Registered fallback Invoice validation: org.peppol.bis:invoice-fallback:3.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register fallback Invoice validation: {}", e.getMessage());
        }
    }

    /**
     * Fallback Credit Note validation with basic XSD only
     */
    private void registerFallbackCreditNoteValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/creditnote/UBL-CreditNote-2.1.xsd"));

            ValidationExecutorSet<IValidationSourceXML> fallbackVES = new ValidationExecutorSet<>(
                DVRCoordinate.create("org.peppol.bis", "creditnote-fallback", "3.0"),
                "Peppol Credit Note Validation (XSD Only - Fallback)",
                ValidationExecutorSetStatus.createValidNow());
            fallbackVES.addExecutor(xsdValidator);

            registry.registerValidationExecutorSet(fallbackVES);
            LOGGER.info("Registered fallback Credit Note validation: org.peppol.bis:creditnote-fallback:3.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register fallback Credit Note validation: {}", e.getMessage());
        }
    }







    /**
     * Register custom document validation (XSD + Schematron)
     */
    private void registerCustomDocValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
        try {
            // Custom document XSD validation
            ValidationExecutorXSD customXsdValidator = ValidationExecutorXSD.create(
                new ClassPathResource("schemas/custom/custom-document.xsd"));

            // Create validation executor set using PHIVE 9.2.2 constructor pattern
            ValidationExecutorSet<IValidationSourceXML> customVES = new ValidationExecutorSet<>(
                    DVRCoordinate.create("com.example", "custom-doc", "1.0"),
                    "Custom Document Validation",
                    ValidationExecutorSetStatus.createValidNow());
            customVES.addExecutor(customXsdValidator);

            // Try to add Schematron validation if available
            try {
                // Create namespace context for Schematron
                MapBasedNamespaceContext aNamespaceContext = new MapBasedNamespaceContext();
                aNamespaceContext.addMapping("c", "urn:example:custom");

                // Custom document Schematron rules
                ValidationExecutorSchematron customSchematronValidator = ValidationExecutorSchematron.createXSLT(
                    new ClassPathResource("schematron/custom/custom-rules.xslt"),
                    aNamespaceContext);

                // Create a new VES with both validators
                customVES = new ValidationExecutorSet<>(
                    DVRCoordinate.create("com.example", "custom-doc", "1.0"),
                    "Custom Document Validation (XSD + Schematron)",
                    ValidationExecutorSetStatus.createValidNow());
                customVES.addExecutor(customXsdValidator);
                customVES.addExecutor(customSchematronValidator);

                LOGGER.info("Added Schematron validation to Custom Document validation");
            } catch (Exception e) {
                LOGGER.warn("Could not add Schematron validation to Custom Document (XSLT file may be missing): {}", e.getMessage());
            }

            registry.registerValidationExecutorSet(customVES);
            LOGGER.info("Registered Custom Document validation: com.example:custom-doc:1.0");

        } catch (Exception e) {
            LOGGER.warn("Could not register Custom Document validation (schema files may be missing): {}", e.getMessage());
        }
    }
}